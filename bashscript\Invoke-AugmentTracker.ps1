#Requires -Version 5.1

<#
.SYNOPSIS
    Augment Code Progress Tracker for PowerShell

.DESCRIPTION
    Simple script to track progress and update instruction files for Augment Code sessions.

.PARAMETER Command
    The command to execute (init, status, complete, start, block, add, log, summary)

.PARAMETER Task
    Task description for complete, start, block, add commands

.PARAMETER Reason
    Reason for blocking a task (used with block command)

.PARAMETER Message
    Log message (used with log command)

.EXAMPLE
    .\Invoke-AugmentTracker.ps1 -Command init
    Initialize a new instructions file

.EXAMPLE
    .\Invoke-AugmentTracker.ps1 -Command complete -Task "Set up project structure"
    Mark a task as completed

.EXAMPLE
    .\Invoke-AugmentTracker.ps1 -Command block -Task "API integration" -Reason "Waiting for API docs"
    Mark a task as blocked
#>

[CmdletBinding()]
param(
    [Parameter(Mandatory = $true, Position = 0)]
    [ValidateSet("init", "status", "complete", "start", "block", "add", "log", "summary", "help")]
    [string]$Command,
    
    [Parameter(Mandatory = $false)]
    [string]$Task,
    
    [Parameter(Mandatory = $false)]
    [string]$Reason,
    
    [Parameter(Mandatory = $false)]
    [string]$Message
)

# Configuration
$InstructionsFile = "augment-instructions.md"
$LogFile = "augment-progress.log"

# Functions
function Write-ColorOutput {
    param(
        [string]$Text,
        [ConsoleColor]$ForegroundColor = [ConsoleColor]::White,
        [string]$Prefix = ""
    )
    
    if ($Prefix) {
        Write-Host "$Prefix " -ForegroundColor $ForegroundColor -NoNewline
    }
    Write-Host $Text -ForegroundColor $ForegroundColor
}

function Add-LogEntry {
    param([string]$Entry)
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    "$timestamp - $Entry" | Add-Content -Path $LogFile
}

function Show-Help {
    Write-ColorOutput -Text "Augment Code Progress Tracker" -ForegroundColor Cyan
    Write-Host "================================"
    Write-Host ""
    Write-Host "Usage: .\Invoke-AugmentTracker.ps1 -Command <command> [options]"
    Write-Host ""
    Write-Host "Commands:"
    Write-Host "  init                    Create new instruction file"
    Write-Host "  status                  Show current progress"
    Write-Host "  complete -Task <task>   Mark task as completed"
    Write-Host "  start -Task <task>      Mark task as in progress"
    Write-Host "  block -Task <task> -Reason <reason>   Mark task as blocked"
    Write-Host "  add -Task <task>        Add new task"
    Write-Host "  log -Message <message>  Add log entry"
    Write-Host "  summary                 Show project summary"
    Write-Host ""
    Write-Host "Examples:"
    Write-Host "  .\Invoke-AugmentTracker.ps1 -Command complete -Task 'Set up project structure'"
    Write-Host "  .\Invoke-AugmentTracker.ps1 -Command start -Task 'User authentication system'"
    Write-Host "  .\Invoke-AugmentTracker.ps1 -Command block -Task 'API integration' -Reason 'Waiting for API docs'"
}

function Initialize-Instructions {
    if (Test-Path $InstructionsFile) {
        Write-ColorOutput -Text "⚠️  Instructions file already exists" -ForegroundColor Yellow
        $response = Read-Host "Overwrite? (y/N)"
        if ($response -notmatch '^[Yy]$') {
            return
        }
    }
    
    if (Test-Path "augment-instructions.md") {
        Copy-Item "augment-instructions.md" $InstructionsFile
        Write-ColorOutput -Text "✅ Instructions file created: $InstructionsFile" -ForegroundColor Green
        Write-ColorOutput -Text "📝 Please edit the file with your project details" -ForegroundColor Blue
        Add-LogEntry "Initialized instructions file"
    } else {
        Write-ColorOutput -Text "❌ Template file not found. Please ensure augment-instructions.md exists" -ForegroundColor Red
        exit 1
    }
}

function Show-Status {
    if (-not (Test-Path $InstructionsFile)) {
        Write-ColorOutput -Text "❌ Instructions file not found. Run with -Command init first" -ForegroundColor Red
        exit 1
    }
    
    Write-ColorOutput -Text "📊 Current Project Status" -ForegroundColor Blue
    Write-Host "=========================="
    
    $content = Get-Content $InstructionsFile
    
    # Extract project name
    $projectLine = $content | Where-Object { $_ -match '^\*\*Project Name\*\*:' }
    if ($projectLine) {
        $projectName = ($projectLine -split '\[|\]')[1]
        Write-ColorOutput -Text "Project: $projectName" -ForegroundColor Green
    }
    
    # Show completed tasks
    Write-Host ""
    Write-ColorOutput -Text "✅ Completed Tasks:" -ForegroundColor Green
    $completedTasks = $content | Where-Object { $_ -match '^- \[x\]' }
    if ($completedTasks) {
        $completedTasks | ForEach-Object {
            $task = ($_ -replace '^- \[x\] ~~(.*)~~.*', '$1')
            Write-Host "  • $task"
        }
    } else {
        Write-Host "  None yet"
    }
    
    # Show in-progress tasks
    Write-Host ""
    Write-ColorOutput -Text "🔄 In Progress:" -ForegroundColor Yellow
    $inProgressTasks = $content | Where-Object { $_ -match '^\s*- \[ \] \*\*IN PROGRESS\*\*' }
    if ($inProgressTasks) {
        $inProgressTasks | ForEach-Object {
            $task = ($_ -replace '.*\*\*IN PROGRESS\*\*:\s*(.*)', '$1')
            Write-Host "  • $task"
        }
    } else {
        Write-Host "  None"
    }
    
    # Show blocked tasks
    Write-Host ""
    Write-ColorOutput -Text "🚫 Blocked:" -ForegroundColor Red
    $blockedTasks = $content | Where-Object { $_ -match '^\s*- \[ \] \*\*BLOCKED\*\*' }
    if ($blockedTasks) {
        $blockedTasks | ForEach-Object {
            $task = ($_ -replace '.*\*\*BLOCKED\*\*:\s*(.*)', '$1')
            Write-Host "  • $task"
        }
    } else {
        Write-Host "  None"
    }
    
    # Show next tasks
    Write-Host ""
    Write-ColorOutput -Text "📋 Next Up:" -ForegroundColor Blue
    $nextUpIndex = ($content | Select-String "### Next Up").LineNumber
    if ($nextUpIndex) {
        $nextTasks = $content[($nextUpIndex)..($nextUpIndex + 4)] | Where-Object { $_ -match '^- \[ \]' } | Select-Object -First 3
        $nextTasks | ForEach-Object {
            $task = ($_ -replace '^- \[ \] (.*)', '$1')
            Write-Host "  • $task"
        }
    }
}

function Complete-Task {
    param([string]$TaskName)
    
    if ([string]::IsNullOrEmpty($TaskName)) {
        Write-ColorOutput -Text "❌ Task description required" -ForegroundColor Red
        return
    }
    
    $content = Get-Content $InstructionsFile
    $date = Get-Date -Format "yyyy-MM-dd"
    
    # Update the task
    $updatedContent = $content -replace "^(\s*)- \[ \] \*\*IN PROGRESS\*\*:\s*$([regex]::Escape($TaskName))", "`$1- [x] ~~$TaskName~~ ($date)"
    $updatedContent = $updatedContent -replace "^(\s*)- \[ \] $([regex]::Escape($TaskName))", "`$1- [x] ~~$TaskName~~ ($date)"
    
    $updatedContent | Set-Content $InstructionsFile
    
    Write-ColorOutput -Text "✅ Marked as completed: $TaskName" -ForegroundColor Green
    Add-LogEntry "COMPLETED: $TaskName"
}

function Start-Task {
    param([string]$TaskName)
    
    if ([string]::IsNullOrEmpty($TaskName)) {
        Write-ColorOutput -Text "❌ Task description required" -ForegroundColor Red
        return
    }
    
    $content = Get-Content $InstructionsFile
    $updatedContent = $content -replace "^(\s*)- \[ \] $([regex]::Escape($TaskName))", "`$1- [ ] **IN PROGRESS**: $TaskName"
    $updatedContent | Set-Content $InstructionsFile
    
    Write-ColorOutput -Text "🔄 Started: $TaskName" -ForegroundColor Yellow
    Add-LogEntry "STARTED: $TaskName"
}

function Block-Task {
    param(
        [string]$TaskName,
        [string]$BlockReason
    )
    
    if ([string]::IsNullOrEmpty($TaskName) -or [string]::IsNullOrEmpty($BlockReason)) {
        Write-ColorOutput -Text "❌ Task description and reason required" -ForegroundColor Red
        return
    }
    
    $content = Get-Content $InstructionsFile
    $updatedContent = $content -replace "^(\s*)- \[ \] $([regex]::Escape($TaskName))", "`$1- [ ] **BLOCKED**: $TaskName - $BlockReason"
    $updatedContent | Set-Content $InstructionsFile
    
    Write-ColorOutput -Text "🚫 Blocked: $TaskName" -ForegroundColor Red
    Write-Host "   Reason: $BlockReason"
    Add-LogEntry "BLOCKED: $TaskName - $BlockReason"
}

function Add-Task {
    param([string]$TaskName)
    
    if ([string]::IsNullOrEmpty($TaskName)) {
        Write-ColorOutput -Text "❌ Task description required" -ForegroundColor Red
        return
    }
    
    $content = Get-Content $InstructionsFile
    $nextUpIndex = ($content | Select-String "### Next Up").LineNumber
    
    if ($nextUpIndex) {
        $content[$nextUpIndex] += "`n- [ ] $TaskName"
        $content | Set-Content $InstructionsFile
    }
    
    Write-ColorOutput -Text "📝 Added task: $TaskName" -ForegroundColor Blue
    Add-LogEntry "ADDED: $TaskName"
}

function Add-Log {
    param([string]$LogMessage)
    
    if ([string]::IsNullOrEmpty($LogMessage)) {
        Write-ColorOutput -Text "❌ Log message required" -ForegroundColor Red
        return
    }
    
    Add-LogEntry "NOTE: $LogMessage"
    Write-ColorOutput -Text "📝 Logged: $LogMessage" -ForegroundColor Blue
}

function Show-Summary {
    if (-not (Test-Path $LogFile)) {
        Write-ColorOutput -Text "⚠️  No log file found" -ForegroundColor Yellow
        return
    }
    
    Write-ColorOutput -Text "📈 Project Summary" -ForegroundColor Blue
    Write-Host "=================="
    
    $logContent = Get-Content $LogFile
    $completedCount = ($logContent | Where-Object { $_ -match "COMPLETED:" }).Count
    $startedCount = ($logContent | Where-Object { $_ -match "STARTED:" }).Count
    $blockedCount = ($logContent | Where-Object { $_ -match "BLOCKED:" }).Count
    
    Write-ColorOutput -Text "Completed Tasks: $completedCount" -ForegroundColor Green
    Write-ColorOutput -Text "Started Tasks: $startedCount" -ForegroundColor Yellow
    Write-ColorOutput -Text "Blocked Tasks: $blockedCount" -ForegroundColor Red
    
    Write-Host ""
    Write-ColorOutput -Text "Recent Activity:" -ForegroundColor Blue
    $logContent | Select-Object -Last 10 | ForEach-Object {
        Write-Host "  $_"
    }
}

# Main script logic
switch ($Command) {
    "init" { Initialize-Instructions }
    "status" { Show-Status }
    "complete" { Complete-Task -TaskName $Task }
    "start" { Start-Task -TaskName $Task }
    "block" { Block-Task -TaskName $Task -BlockReason $Reason }
    "add" { Add-Task -TaskName $Task }
    "log" { Add-Log -LogMessage $Message }
    "summary" { Show-Summary }
    "help" { Show-Help }
    default { Show-Help }
}
