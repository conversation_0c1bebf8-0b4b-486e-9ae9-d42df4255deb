<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Simple To-Do List</title>
  <style>
    /* PRD 4.4.1: Input field 44px height with focus state */
    :root {
      --primary-color: #2E86AB;
      --completed-color: #6c757d;
      --error-color: #dc3545;
    }

    * {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }

    body {
      font-family: 'Segoe UI', Roboto, sans-serif;
      line-height: 1.5;
      color: #333;
      max-width: 800px;
      margin: 2rem auto;
      padding: 0 1rem;
    }

    h1 {
      text-align: center;
      margin-bottom: 1.5rem;
      font-size: 1.8rem;
    }

    .task-input-container {
      display: flex;
      gap: 0.5rem;
      margin-bottom: 1.5rem;
    }

    #task-input {
      flex: 1;
      padding: 0.75rem;
      border: 2px solid #ddd;
      border-radius: 4px;
      font-size: 1rem;
      height: 44px; /* PRD 4.4.1: 44px touch target */
      transition: border-color 0.2s;
    }

    #task-input:focus {
      outline: none;
      border-color: var(--primary-color); /* PRD 4.4.1: 2px primary color outline */
      box-shadow: 0 0 0 2px rgba(46, 134, 171, 0.2);
    }

    #add-task {
      background-color: var(--primary-color);
      color: white;
      border: none;
      border-radius: 4px;
      padding: 0 1.25rem;
      font-size: 1rem;
      cursor: pointer;
      transition: background-color 0.2s;
      height: 44px; /* PRD 4.4.1: 44px touch target */
    }

    #add-task:hover {
      background-color: #236fa1;
    }

    #add-task:active {
      transform: translateY(1px);
    }

    .task-list {
      max-height: 70vh; /* PRD 4.2.2: 70vh max height */
      overflow-y: auto;
      border: 1px solid #eee;
      border-radius: 4px;
      padding: 0.5rem;
    }

    .task-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0.75rem;
      border-bottom: 1px solid #eee;
      animation: fadeIn 0.3s; /* PRD 4.4.4: Smooth entry */
    }

    .task-content {
      flex: 1;
      word-break: break-word;
    }

    .completed .task-content {
      text-decoration: line-through 2px 35deg; /* PRD 4.2.2: 2px strikethrough at 35° */
      opacity: 0.6; /* PRD 4.2.1: 60% opacity for completed */
      color: var(--completed-color); /* PRD 4.2.1: #6c757d text color */
    }

    .task-actions {
      display: flex;
      gap: 0.75rem;
    }

    .task-action {
      width: 24px;
      height: 24px;
      padding: 8px; /* PRD 4.4.2: 8px padding around 24x24px icon */
      border: none;
      background: none;
      cursor: pointer;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: background-color 0.2s;
    }

    .task-action:hover {
      background-color: #f0f0f0;
    }

    .task-action:focus {
      outline: 2px solid var(--primary-color);
      border-radius: 50%;
    }

    .delete-task {
      color: #dc3545;
    }

    .section-header {
      font-weight: bold;
      margin: 1rem 0 0.5rem;
      color: #555;
    }

    /* PRD 4.5.3: Minimum 48x48px effective touch target */
    @media (max-width: 480px) {
      body {
        padding: 0 0.5rem;
      }
      
      #task-input {
        width: 100%;
      }
    }

    @keyframes fadeIn {
      from { opacity: 0; transform: translateY(5px); }
      to { opacity: 1; transform: translateY(0); }
    }

    @keyframes scaleDown {
      from { transform: scale(1); }
      to { transform: scale(0.95); opacity: 0; }
    }

    .scale-down {
      animation: scaleDown 0.1s forwards; /* PRD 4.4.4: 100ms scale-down animation */
    }
  </style>
</head>
<body>
  <h1>Simple To-Do List</h1>
  
  <div class="task-input-container">
    <input 
      type="text" 
      id="task-input" 
      placeholder="Add a new task..." 
      aria-label="Task input field"
      autocomplete="off"
    >
    <button id="add-task" aria-label="Add new task">Add</button>
  </div>
  
  <div id="pending-tasks">
    <h2 class="section-header">Pending Tasks</h2>
    <div class="task-list" id="pending-task-list"></div>
  </div>
  
  <div id="completed-tasks">
    <h2 class="section-header">Completed Tasks</h2>
    <div class="task-list" id="completed-task-list"></div>
  </div>

  <script>
    // PRD 3.1: Task Creation
    // ARCH 3.1: MVC Pattern - Model (TaskStore)
    class TaskStore {
      constructor() {
        this.tasks = [];
        this.eventDispatcher = new EventDispatcher();
      }

      constructor() {
        this.tasks = [];
        this.eventDispatcher = new EventDispatcher();
        this.loadFromStorage(); // Load tasks from localStorage on initialization
      }

      loadFromStorage() {
        try {
          const storedTasks = localStorage.getItem('tasks');
          if (storedTasks) {
            this.tasks = JSON.parse(storedTasks);
            this._notify('tasksLoaded', this.tasks);
          }
        } catch (e) {
          console.error('Error loading tasks from storage', e);
        }
      }

      saveToStorage() {
        try {
          localStorage.setItem('tasks', JSON.stringify(this.tasks));
        } catch (e) {
          console.error('Error saving tasks to storage', e);
          this._handleStorageError();
        }
      }

      _handleStorageError() {
        // PRD 5.4.2: Non-intrusive warning at 2MB capacity
        const warning = document.createElement('div');
        warning.className = 'storage-warning';
        warning.textContent = 'Storage limit reached. Some tasks may not be saved.';
        warning.style.cssText = `
          position: fixed;
          bottom: 1rem;
          left: 50%;
          transform: translateX(-50%);
          background: #dc3545;
          color: white;
          padding: 0.5rem 1rem;
          border-radius: 4px;
          z-index: 1000;
          animation: fadeInOut 3s forwards;
        `;
        
        document.body.appendChild(warning);
        
        setTimeout(() => {
          warning.remove();
        }, 3000);
      }

      addTask(text) {
        if (!text.trim()) return false; // PRD 3.1: Empty submissions rejected silently
        
        const newTask = {
          id: this._generateId(),
          text: text.substring(0, 256), // PRD 3.1: Max 256 chars
          completed: false,
          createdAt: Date.now()
        };
        
        this.tasks.push(newTask);
        this._notify('taskAdded', newTask);
        this.saveToStorage(); // PRD 3.4: Automatic synchronization with localStorage
        return true;
      }

      toggleCompletion(id) {
        const task = this.tasks.find(t => t.id === id);
        if (task) {
          task.completed = !task.completed;
          this._notify('taskToggled', task);
          this.saveToStorage(); // PRD 3.4: Automatic synchronization with localStorage
        }
      }

      deleteTask(id) {
        const index = this.tasks.findIndex(t => t.id === id);
        if (index !== -1) {
          const task = this.tasks[index];
          this.tasks.splice(index, 1);
          this._notify('taskDeleted', task);
          this.saveToStorage(); // PRD 3.4: Automatic synchronization with localStorage
        }
      }

      _generateId() {
        return 'task-' + Math.random().toString(36).substr(2, 9);
      }

      _notify(event, data) {
        this.eventDispatcher.dispatch(event, data);
      }

      subscribe(event, callback) {
        this.eventDispatcher.subscribe(event, callback);
      }
    }

    // ARCH 3.2: Observer Pattern
    class EventDispatcher {
      constructor() {
        this.listeners = {};
      }

      subscribe(event, callback) {
        if (!this.listeners[event]) {
          this.listeners[event] = [];
        }
        this.listeners[event].push(callback);
      }

      dispatch(event, data) {
        if (this.listeners[event]) {
          this.listeners[event].forEach(callback => callback(data));
        }
      }
    }

    // ARCH 3.1: MVC Pattern - View (UIController)
    class UIController {
      constructor(taskStore) {
        this.taskStore = taskStore;
        this.pendingList = document.getElementById('pending-task-list');
        this.completedList = document.getElementById('completed-task-list');
        this.taskInput = document.getElementById('task-input');
        
        this._setupEventListeners();
        this._renderInitialView();
        this._setupAriaLiveRegions();
      }

      _setupEventListeners() {
        document.getElementById('add-task').addEventListener('click', () => this._handleAddTask());
        this.taskInput.addEventListener('keypress', (e) => {
          if (e.key === 'Enter') this._handleAddTask();
        });
      }

      _handleAddTask() {
        const text = this.taskInput.value;
        const success = this.taskStore.addTask(text);
        
        if (success) {
          this.taskInput.value = '';
          this.taskInput.focus();
        }
      }

      _renderInitialView() {
        // Initial view setup handled through rendering methods
      }

      _setupAriaLiveRegions() {
        // PRD 4.3: ARIA live regions for dynamic content
        const pendingRegion = document.createElement('div');
        pendingRegion.setAttribute('aria-live', 'polite');
        pendingRegion.setAttribute('class', 'sr-only');
        document.body.appendChild(pendingRegion);
        
        const completedRegion = document.createElement('div');
        completedRegion.setAttribute('aria-live', 'polite');
        completedRegion.setAttribute('class', 'sr-only');
        document.body.appendChild(completedRegion);
      }

      renderTask(task) {
        const taskElement = document.createElement('div');
        taskElement.className = `task-item ${task.completed ? 'completed' : ''}`;
        taskElement.id = task.id;
        taskElement.setAttribute('role', 'listitem');
        
        taskElement.innerHTML = `
          <div class="task-content">${this._escapeHtml(task.text)}</div>
          <div class="task-actions">
            <button class="task-action toggle-complete" aria-label="Mark task as complete">✓</button>
            <button class="task-action delete-task" aria-label="Delete task">×</button>
          </div>
        `;
        
        // PRD 4.4.2: 8px padding around 24x24px icon
        const toggleBtn = taskElement.querySelector('.toggle-complete');
        const deleteBtn = taskElement.querySelector('.delete-task');
        
        toggleBtn.addEventListener('click', () => {
          // ARCH 3.2: Observer pattern will handle state update
          this.taskStore.toggleCompletion(task.id);
        });
        
        deleteBtn.addEventListener('click', () => {
          // ARCH 3.2: Observer pattern will handle state update
          this.taskStore.deleteTask(task.id);
        });
        
        // PRD 4.5.3: Minimum 48x48px effective touch target
        [toggleBtn, deleteBtn].forEach(btn => {
          btn.addEventListener('focus', () => {
            btn.style.outline = '2px solid var(--primary-color)';
          });
          btn.addEventListener('blur', () => {
            btn.style.outline = '';
          });
        });
        
        return taskElement;
      }

      _escapeHtml(unsafe) {
        return unsafe
          .replace(/&/g, "&")
          .replace(/</g, "<")
          .replace(/>/g, ">")
          .replace(/"/g, '"')
          .replace(/'/g, '&#039;');
      }

      // Additional UI methods would be implemented here
    }

    // ARCH 3.1: MVC Pattern - Controller (TaskManager)
    class TaskManager {
      constructor(taskStore, uiController) {
        this.taskStore = taskStore;
        this.uiController = uiController;
        
        this.taskStore.subscribe('taskAdded', (task) => {
          this._handleTaskAdded(task);
        });
      }

      _handleTaskAdded(task) {
        // PRD 3.1.1: Task appears within 300ms
        const taskElement = this.uiController.renderTask(task);
        this._insertTaskInChronologicalOrder(taskElement, task);
      }

      _insertTaskInChronologicalOrder(taskElement, task) {
        const targetList = task.completed 
          ? this.uiController.completedList 
          : this.uiController.pendingList;
        
        // Insert at top for pending tasks (PRD 5.3.2)
        if (!task.completed && targetList.firstChild) {
          targetList.insertBefore(taskElement, targetList.firstChild);
        } else {
          targetList.appendChild(taskElement);
        }
      }
    }

    // ARCH 3.3: Facade Pattern
    const App = {
      init() {
        const taskStore = new TaskStore();
        const uiController = new UIController(taskStore);
        new TaskManager(taskStore, uiController);
      }
    };

    // Initialize app when DOM is ready
    document.addEventListener('DOMContentLoaded', () => {
      App.init();
      
      // PRD 3.1.1: Verify 300ms response time
      console.log('App initialized - ready for task creation within 300ms');
    });
  </script>
</body>
</html>